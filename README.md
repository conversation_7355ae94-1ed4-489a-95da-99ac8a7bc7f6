## 项目介绍

数字天启服务端，基于 hyperf 2.2+

### 使用
#### 创建模型
```shell
# redmine库
php bin/hyperf.php gen:model user_department --path=app/Model/Redmine --pool=tchip_redmine
```

#### 查看所有路由
```shell
php bin/hyperf.php describe:routes
```

### 更新百度统计数据
```shell
php bin/hyperf.php tongjiBd:SiteReportCommand --dated 2022.07.01-2022.07.19
```

#### 创建执行数据表迁移
```shell
# create_table 创建表格式create + 表名
# commit_attr_table 更新表字段 commit + 字段名 + 名表
php bin/hyperf.php gen:migration commit_attr_table
# 运行迁移
php bin/hyperf.php migrate
```

#### 企业微信OAuth2.0网页授权
进入企业微信后台管理后，找到数字天启应用， 设置网页授权及JS-SDK可信域名







# Introduction

This is a skeleton application using the Hyperf framework. This application is meant to be used as a starting place for those looking to get their feet wet with Hyperf Framework.

# Requirements

Hyperf has some requirements for the system environment, it can only run under Linux and Mac environment, but due to the development of Docker virtualization technology, Docker for Windows can also be used as the running environment under Windows.

The various versions of Dockerfile have been prepared for you in the [hyperf/hyperf-docker](https://github.com/hyperf/hyperf-docker) project, or directly based on the already built [hyperf/hyperf](https://hub.docker.com/r/hyperf/hyperf) Image to run.

When you don't want to use Docker as the basis for your running environment, you need to make sure that your operating environment meets the following requirements:  

 - PHP >= 7.3
 - Swoole PHP extension >= 4.5，and Disabled `Short Name`
 - OpenSSL PHP extension
 - JSON PHP extension
 - PDO PHP extension （If you need to use MySQL Client）
 - Redis PHP extension （If you need to use Redis Client）
 - Protobuf PHP extension （If you need to use gRPC Server of Client）

# Installation using Composer

The easiest way to create a new Hyperf project is to use Composer. If you don't have it already installed, then please install as per the documentation.

To create your new Hyperf project:

$ composer create-project hyperf/hyperf-skeleton path/to/install

Once installed, you can run the server immediately using the command below.

$ cd path/to/install
$ php bin/hyperf.php start

This will start the cli-server on port `9501`, and bind it to all network interfaces. You can then visit the site at `http://localhost:9501/`

which will bring up Hyperf default home page.

### 问题集

Class name must be a valid object or a string[83] in /var/www/runtime/container/proxy/App_Core_Services_BusinessService.proxy.php

有可能是注解中使用了 @Inject() ，即需要改回@Inject

