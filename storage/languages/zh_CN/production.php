<?php
return [
    'Production_not_exit' => '订单信息不存在',
    'Not_Continuous'      => '从%s开始,到%s结束，已有%s个在使用',
    'Greater_start_mac' => '使用的初始号 %s, 小于公司购买的初始号 %s',
    'Greater_end_mac'                          => '使用的结束号 %s, 大于公司购买的结束号 %s',
    'Start_mac_exist'     => '起始MAC地址已在使用',
    'Greater_max_exist'                        => '存在大于当前起始号 %s ,的MAC地址 %s',
    'Mac_address_exist'                        => '当前编辑的MAC地址已存在',
    'Mac_reduce_insufficient'                  => 'mac数量不足%u, 无法减少',
    'Not_exit_mac'                             => '没有存在绑定地址',
    'Has_other_sync_task' => '已经有同步任务在执行，请稍后再操作',
    'Exist_order_code' =>'订单号已存在',
    'Not_exit_stock_order'=>'备货单不存在',
    'Sn_code_exist'=>'序列号已存在',
    'Sn_code_not_exist'=>'SN号未在系统录入',
    'Attachment_not_exist' => '附件不存在',
    'Formula_not_met'=>'条件未满足，不允许进行操作'
];