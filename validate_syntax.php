<?php

// 简单的语法验证脚本
$file = 'app/Core/Services/Project/IssueService.php';

if (!file_exists($file)) {
    echo "文件不存在: $file\n";
    exit(1);
}

// 检查PHP语法
$output = [];
$return_var = 0;
exec("php -l $file 2>&1", $output, $return_var);

if ($return_var === 0) {
    echo "✅ PHP语法检查通过: $file\n";
    echo implode("\n", $output) . "\n";
} else {
    echo "❌ PHP语法错误: $file\n";
    echo implode("\n", $output) . "\n";
    exit(1);
}

// 检查类是否可以被加载（简单检查）
try {
    $content = file_get_contents($file);
    
    // 检查关键方法是否存在
    $methods = [
        'getAllChildIssues',
        'getIssuesCustomFields',
        'getIssuesWatchers', 
        'getIssuesAssignedUsers',
        'batchCopyIssue'
    ];
    
    foreach ($methods as $method) {
        if (strpos($content, "function $method") !== false) {
            echo "✅ 方法存在: $method\n";
        } else {
            echo "❌ 方法缺失: $method\n";
        }
    }
    
    // 检查关键修改点
    if (strpos($content, "copy_children") !== false) {
        echo "✅ copy_children 功能已实现\n";
    } else {
        echo "❌ copy_children 功能缺失\n";
    }
    
    if (strpos($content, "getAllChildIssues(\$selectedIssueIds)") !== false) {
        echo "✅ 子事项获取逻辑已实现\n";
    } else {
        echo "❌ 子事项获取逻辑缺失\n";
    }
    
    echo "\n✅ 所有检查完成！\n";
    
} catch (Exception $e) {
    echo "❌ 检查过程中出错: " . $e->getMessage() . "\n";
    exit(1);
}
