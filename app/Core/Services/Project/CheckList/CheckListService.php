<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2025/05/27 16:36
     * <AUTHOR>
     * @Description
     */

    namespace App\Core\Services\Project\CheckList;

    use Carbon\Carbon;
    use Hyperf\Context\Context;
    use Hyperf\Database\Model\Builder;
    use Hyperf\Database\Model\Collection;
    use Hyperf\Database\Model\Model;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;
    use App\Model\Redmine\CheckList\ChecklistModel;
    use App\Model\Redmine\CheckList\ChecklistItemModel;
    use App\Model\Redmine\CheckList\ChecklistTemplateModel;
    use App\Model\Redmine\CheckList\ChecklistTemplateItemModel;
    use App\Model\Redmine\CheckList\ChecklistFieldSettingModel;
    use Hyperf\Utils\ApplicationContext;
    use Parsedown;
    use Qbhy\HyperfAuth\AuthManager;
    use Hyperf\Utils\Coroutine;
    use Hyperf\Utils\Parallel;
    use League\HTMLToMarkdown\HtmlConverter;
    use Swoole\Coroutine\Channel;
    use Hyperf\Paginator\Paginator;
    use App\Exception\AppException;
    use App\Constants\StatusCode;

    class CheckListService extends \App\Core\Services\BusinessService
    {

           /**
         * @Inject()
         * @var ChecklistModel
         */
        protected $checklistModel;

        /**
         * @Inject()
         * @var ChecklistItemModel
         */
        protected $checklistItemModel;

        /**
         * @Inject()
         * @var ChecklistTemplateModel
         */
        protected $checklistTemplateModel;
        

        /**
         * @Inject()
         * @var ChecklistTemplateItemModel
         */
        protected $checklistTemplateItemModel;
        
        
        /**
         * @Inject()
         * @var ChecklistFieldSettingModel
         */
        protected $checklistFieldSettingModel;
        
        /**
         * @Inject()
         * @var AuthManager
         */
        protected AuthManager $auth;
        
        /////////////////////////////////////////////
        /// 模板相关接口
        /**
         * 获取预设清单模板列表
         * @param array $filter 筛选条件
         * @param array $op 操作符
         * @param string $sort 排序字段
         * @param string $order 排序方式
         * @param int $limit 每页数量
         * @param int $page 页码
         * @return array
         */
        public function getTemplateList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, int $page = 1)
        {
            $query = $this->checklistTemplateModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 处理软删除
            $query->whereNull('deleted_at');
            
            $paginate = $query->orderBy($sort, $order)->paginate($limit);
            return $paginate ? $paginate->toArray() : [];
        }
        
        /**
         * 获取模板详情
         * @param int $templateId 模板ID
         * @return array 模板详情及其清单项
         */
        public function getTemplateDetail(int $templateId)
        {
            // 获取模板基本信息
            $template = $this->checklistTemplateModel::query()->find($templateId);
            if (!$template) {
                throw new AppException(StatusCode::ERR_SERVER, __('模板不存在'));
            }
            
            $result = $template->toArray();
            
            // 获取模板下的所有清单项
            $items = $this->checklistTemplateItemModel::query()
                ->where('template_id', $templateId)
                ->whereNull('deleted_at')
                ->orderBy('sort_order', 'ASC')
                ->get();
            
            $result['items'] = $items ? $items->toArray() : [];
            
            return $result;
        }
        
        /**
         * 新增/编辑模板
         * @param int $templateId 模板ID（0表示新增）
         * @param array $templateData 模板数据
         * @param array $items 模板项数据
         * @return array 保存后的模板数据
         */
        public function saveTemplate(int $templateId, array $templateData, array $items = [])
        {
            Db::beginTransaction();
            try {
                $userId = $this->auth->user()->getId();
                
                // 处理模板基本信息
                if ($templateId > 0) {
                    // 编辑模式
                    $template = $this->checklistTemplateModel::query()->find($templateId);
                    if (!$template) {
                        throw new AppException(StatusCode::ERR_SERVER, __('模板不存在'));
                    }
                    
                    // 更新模板基本信息
                    $template->fill($templateData);
                    $template->save();
                } else {
                    // 新增模式
                    $templateData['created_by'] = $userId;
                    $template = $this->checklistTemplateModel::query()->create($templateData);
                    $templateId = $template->id;
                }
                
                // 处理模板项
                $existingItems = $this->checklistTemplateItemModel::query()
                    ->where('template_id', $templateId)
                    ->get()
                    ->keyBy('id')
                    ->toArray();

                $processedIds = [];

                // 处理提交的项目
                foreach ($items as $index => $item) {
                    if (isset($item['id']) && $item['id'] > 0) {
                        // 更新现有项目
                        if (isset($existingItems[$item['id']])) {
                            $this->checklistTemplateItemModel::query()
                                ->where('id', $item['id'])
                                ->update([
                                    'item_title' => $item['item_title'],
                                    'sort_order' => $item['sort_order'] ?? ($index + 1)
                                ]);
                            $processedIds[] = $item['id'];
                        } else {
                            // id不存在时作为新项目处理
                            $newItem = $this->checklistTemplateItemModel::query()->create([
                                'template_id' => $templateId,
                                'item_title' => $item['item_title'],
                                'sort_order' => $item['sort_order'] ?? ($index + 1)
                            ]);
                            $processedIds[] = $newItem->id;
                        }
                    } else {
                        // id为-1或不存在时作为新项目处理
                        $newItem = $this->checklistTemplateItemModel::query()->create([
                            'template_id' => $templateId,
                            'item_title' => $item['item_title'],
                            'sort_order' => $item['sort_order'] ?? ($index + 1)
                        ]);
                        $processedIds[] = $newItem->id;
                    }
                }

                // 删除未包含在提交数据中的项目
                $itemsToDelete = array_diff(array_keys($existingItems), $processedIds);
                if (!empty($itemsToDelete)) {
                    $this->checklistTemplateItemModel::query()
                        ->whereIn('id', $itemsToDelete)
                        ->delete(); // 或者使用软删除 ->update(['deleted_at' => Carbon::now()]);
                }
                
                Db::commit();
                
                // 返回保存后的完整模板数据
                return $this->getTemplateDetail($templateId);
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        
        /**
         * 删除模板
         * @param int $templateId 模板ID
         * @return bool 是否成功
         */
        public function deleteTemplate(int $templateId)
        {
            Db::beginTransaction();
            try {
                // 检查模板是否存在
                $template = $this->checklistTemplateModel::query()->find($templateId);
                if (!$template) {
                    throw new AppException(StatusCode::ERR_SERVER, __('模板不存在'));
                }
                
                // 软删除模板
                $template->deleted_at = Carbon::now();
                $template->save();
                
                // 软删除模板项
                $this->checklistTemplateItemModel::query()
                    ->where('template_id', $templateId)
                    ->update(['deleted_at' => Carbon::now()]);
                
                Db::commit();
                return true;
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        
        /////////////////////////////////////////////
        /// 检查清单相关接口
        /**
         * 获取检查清单列表
         * @param array $filter 筛选条件
         * @param array $op 操作符
         * @param string $sort 排序字段
         * @param string $order 排序方式
         * @param int $limit 每页数量
         * @param int $page 页码
         * @return array
         */
        public function getChecklistList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, int $page = 1)
        {
            $query = $this->checklistModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            
            // 处理软删除
            $query->whereNull('deleted_at');
            
            $paginate = $query->orderBy($sort, $order)->paginate($limit);
            
            if ($paginate) {
                $result = $paginate->toArray();
                
                // 获取每个检查清单的完成状态统计
                foreach ($result['data'] as &$checklist) {
                    $stats = $this->checklistItemModel::query()
                        ->where('checklist_id', $checklist['id'])
                        ->whereNull('deleted_at')
                        ->selectRaw('status, COUNT(*) as count')
                        ->groupBy('status')
                        ->get()
                        ->pluck('count', 'status')
                        ->toArray();
                    
                    $checklist['completed_count'] = $stats[1] ?? 0;
                    $checklist['total_count'] = array_sum($stats);
                    $checklist['completion_rate'] = $checklist['total_count'] > 0 
                        ? round(($checklist['completed_count'] / $checklist['total_count']) * 100, 2) 
                        : 0;
                }
                
                return $result;
            }
            
            return [];
        }
        
        /**
         * 获取检查清单详情
         * @param int $checklistId 检查清单ID
         * @return array 检查清单详情及其清单项
         */
        public function getChecklistDetail(int $checklistId)
        {
            // 获取检查清单基本信息
            $checklist = $this->checklistModel::query()->find($checklistId);
            if (!$checklist) {
                throw new AppException(StatusCode::ERR_SERVER, __('检查清单不存在'));
            }
            
            $result = $checklist->toArray();
            
            // 获取检查清单下的所有清单项
            $items = $this->checklistItemModel::query()
                ->where('checklist_id', $checklistId)
                ->whereNull('deleted_at')
                ->orderBy('sort_order', 'ASC')
                ->get();
            
            $result['items'] = $items ? $items->toArray() : [];
            
            // 计算完成状态
            $completed = 0;
            $total = count($result['items']);
            
            foreach ($result['items'] as $item) {
                if ($item['status'] == 1) {
                    $completed++;
                }
            }
            
            $result['completed_count'] = $completed;
            $result['total_count'] = $total;
            $result['completion_rate'] = $total > 0 ? round(($completed / $total) * 100, 2) : 0;
            
            return $result;
        }
        
        /**
         * 从模板创建检查清单
         * @param int $templateId 模板ID
         * @param array $checklistData 检查清单数据
         * @return array 创建后的检查清单数据
         */
        public function createChecklistFromTemplate(int $templateId, array $checklistData)
        {
            Db::beginTransaction();
            try {
                $userId = $this->auth->user()->getId();
                
                // 获取模板详情
                $template = $this->getTemplateDetail($templateId);
                
                // 创建检查清单
                $checklistData['created_by'] = $userId;
                $checklist = $this->checklistModel::query()->create($checklistData);
                
                // 创建检查清单项
                foreach ($template['items'] as $index => $item) {
                    $this->checklistItemModel::query()->create([
                        'checklist_id' => $checklist->id,
                        'item_title' => $item['item_title'],
                        'status' => 0, // 默认未完成
                        'sort_order' => $item['sort_order'] ?? ($index + 1),
                        'custom_fields' => null
                    ]);
                }
                
                Db::commit();
                
                // 返回创建后的检查清单详情
                return $this->getChecklistDetail($checklist->id);
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        
        /**
         * 新增/编辑检查清单
         * @param int $checklistId 检查清单ID（0表示新增）
         * @param array $checklistData 检查清单数据
         * @param array $items 检查清单项数据
         * @return array 保存后的检查清单数据
         */
        public function saveChecklist(int $checklistId, array $checklistData, array $items = [])
        {
            Db::beginTransaction();
            try {
                $userId = $this->auth->user()->getId();
                
                // 处理检查清单基本信息
                if ($checklistId > 0) {
                    // 编辑模式
                    $checklist = $this->checklistModel::query()->find($checklistId);
                    if (!$checklist) {
                        throw new AppException(StatusCode::ERR_SERVER, __('检查清单不存在'));
                    }
                    
                    // 更新检查清单基本信息
                    $checklist->fill($checklistData);
                    $checklist->save();
                } else {
                    // 新增模式
                    $checklistData['created_by'] = $userId;
                    $checklist = $this->checklistModel::query()->create($checklistData);
                    $checklistId = $checklist->id;
                }
                
                // 处理检查清单项
                // 获取现有项目ID（仅在编辑模式下）
                $existingItemIds = [];
                if ($checklistId > 0) {
                    $existingItemIds = $this->checklistItemModel::query()
                        ->where('checklist_id', $checklistId)
                        ->pluck('id')
                        ->toArray();
                }
                
                // 处理传入的项目（无论是否为空）
                $processedItemIds = [];
                foreach ($items as $index => $item) {
                    if (isset($item['id']) && $item['id'] > 0) {
                        // 更新现有项目
                        $checklistItem = $this->checklistItemModel::query()->find($item['id']);
                        if ($checklistItem && $checklistItem->checklist_id == $checklistId) {
                            $checklistItem->fill([
                                'item_title' => $item['item_title'],
                                'status' => $item['status'] ?? 0,
                                'remarks' => $item['remarks'] ?? null,
                                'sort_order' => $item['sort_order'] ?? ($index + 1),
                                'custom_fields' => $item['custom_fields'] ?? null
                            ]);
                            $checklistItem->save();
                            $processedItemIds[] = $item['id'];
                        }
                    } else {
                        // 添加新项目
                        $newItem = $this->checklistItemModel::query()->create([
                            'checklist_id' => $checklistId,
                            'item_title' => $item['item_title'],
                            'status' => $item['status'] ?? 0,
                            'remarks' => $item['remarks'] ?? null,
                            'sort_order' => $item['sort_order'] ?? ($index + 1),
                            'custom_fields' => $item['custom_fields'] ?? null,
                            'created_by' => $item['created_by'] ?? $userId
                        ]);
                        $processedItemIds[] = $newItem->id;
                    }
                }

                // 删除未包含在处理列表中的项目
                $itemsToDelete = array_diff($existingItemIds, $processedItemIds);
                if (!empty($itemsToDelete)) {
                    $this->checklistItemModel::query()
                        ->whereIn('id', $itemsToDelete)
                        ->update(['deleted_at' => Carbon::now()]);
                }
                
                Db::commit();
                
                // 返回保存后的完整检查清单数据
                return $this->getChecklistDetail($checklistId);
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        
        /**
         * 更新检查清单项状态
         * @param int $itemId 检查清单项ID
         * @param int $status 状态（0=未完成；1=已完成）
         * @return bool 是否成功
         */
        public function updateChecklistItemStatus(int $itemId, int $status)
        {
            // 检查项目是否存在
            $item = $this->checklistItemModel::query()->find($itemId);
            if (!$item) {
                throw new AppException(StatusCode::ERR_SERVER, __('检查项不存在'));
            }
            
            // 更新状态
            $item->status = $status;
            $item->save();
            
            return true;
        }
        
        /**
         * 删除检查清单
         * @param int $checklistId 检查清单ID
         * @return bool 是否成功
         */
        public function deleteChecklist(int $checklistId)
        {
            Db::beginTransaction();
            try {
                // 检查清单是否存在
                $checklist = $this->checklistModel::query()->find($checklistId);
                if (!$checklist) {
                    throw new AppException(StatusCode::ERR_SERVER, __('检查清单不存在'));
                }
                
                // 软删除检查清单
                $checklist->deleted_at = Carbon::now();
                $checklist->save();
                
                // 软删除检查清单项
                $this->checklistItemModel::query()
                    ->where('checklist_id', $checklistId)
                    ->update(['deleted_at' => Carbon::now()]);
                
                Db::commit();
                return true;
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        
        /////////////////////////////////////////////
        /// 字段设置相关接口
        /**
         * 获取字段设置列表
         * @param int|null $projectId 项目ID（null表示获取全局设置）
         * @return array 字段设置列表
         */
        public function getFieldSettings(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, int $page = 1)
        {
            $query = $this->checklistFieldSettingModel::query();
            [$query, $limit, $sort, $order] = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            $projectId = $filter['project_id'];

            $query->where('project_id', $projectId);
            
            $settings = $query->whereNull('deleted_at')
                ->orderBy($sort, $order)
                ->paginate($limit);

            // 如果没有找到对应的设置，初始化默认设置
            if ($settings->isEmpty()) {
                $this->initDefaultFieldSettings($projectId);
                // 重新查询设置
                $settings = $query->whereNull('deleted_at')
                    ->orderBy($sort, $order)
                    ->paginate($limit);
            }

            return $settings ? $settings->toArray() : [];
        }
        
        /**
         * 保存字段设置
         * @param array $settings 字段设置数组
         * @return bool 是否成功
         */
        public function saveFieldSettings(array $settings)
        {
            Db::beginTransaction();
            try {
                // 获取现有设置
                $query = $this->checklistFieldSettingModel::query();

                $existingSettings = $query->get()->keyBy('id')->toArray();
                
                // 处理新设置
                $processedIds = [];
                foreach ($settings as $index => $setting) {
                    if (isset($setting['id']) && $setting['id'] > 0) {
                        // 更新现有设置
                        if (isset($existingSettings[$setting['id']])) {
                            $this->checklistFieldSettingModel::query()
                                ->where('id', $setting['id'])
                                ->update([
                                    'field_name' => $setting['field_name'],
                                    'display_name' => $setting['display_name'],
                                    'is_enabled' => $setting['is_enabled'] ?? 1,
                                    'sort_order' => $setting['sort_order'] ?? ($index + 1)
                                ]);
                            $processedIds[] = $setting['id'];
                        }
                    } else {
                        // 创建新设置
                        $newSetting = $this->checklistFieldSettingModel::query()->create([
                            'field_name' => $setting['field_name'],
                            'display_name' => $setting['display_name'],
                            'is_enabled' => $setting['is_enabled'] ?? 1,
                            'sort_order' => $setting['sort_order'] ?? ($index + 1)
                        ]);
                        $processedIds[] = $newSetting->id;
                    }
                }
                
                // 删除未处理的设置
                $idsToDelete = array_diff(array_keys($existingSettings), $processedIds);
                if (!empty($idsToDelete)) {
                    $this->checklistFieldSettingModel::query()
                        ->whereIn('id', $idsToDelete)
                        ->delete();
                }
                
                Db::commit();
                return true;
                
            } catch (\Exception $e) {
                Db::rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 删除字段
         * @param int $settingId 字段设置ID
         * @return bool 是否成功
         */
        public function deleteFieldSetting(int $settingId)
        {
            $setting = $this->checklistFieldSettingModel::query()->find($settingId);
            if (!$setting) {
                throw new AppException(StatusCode::ERR_SERVER, __('字段不存在'));
            }
            
            $setting->delete();
            
            return true;
        }
        
        
        /**
         * 初始化默认字段设置
         * @param int|null $projectId 项目ID（null表示全局设置）
         * @return bool 是否成功
         */
        public function initDefaultFieldSettings(?int $projectId = null)
        {
            try {
                // 检查是否已存在该项目的字段设置
                $existingSettings = $this->checklistFieldSettingModel::query()
                    ->where('project_id', $projectId)
                    ->exists();
                
                if (!$existingSettings) {
                    // 如果不存在，则添加默认字段设置
                    $this->checklistFieldSettingModel::query()->create([
                        'project_id' => $projectId,
                        'field_name' => 'item_title',
                        'display_name' => '标题',
                        'is_enabled' => 1,
                        'sort_order' => 1
                    ]);
                }
                
                return true;
            } catch (\Exception $e) {
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 检查问题日志以重置检查清单项状态
         * 根据问题日志中的操作记录，重置相关的检查清单项状态
         * 
         * @param int $issueId 问题ID
         * @return bool 是否成功重置状态
         */
        public function resetCheckListItemStatusByIssueJournal(int $issueId): bool
        {
            try {
                // 获取最新的状态变更记录
                $journalModel = make(\App\Model\Redmine\JournalsModel::class);
                $journalDetailModel = make(\App\Model\Redmine\JournalDetailsModel::class);
                
                // 查询最新的状态变更记录
                $latestStatusChange = $journalDetailModel::query()
                    ->join('journals', 'journal_details.journal_id', '=', 'journals.id')
                    ->where('journals.journalized_id', $issueId)
                    ->where('journals.journalized_type', 'Issue')
                    ->where('journal_details.property', 'attr')
                    ->where('journal_details.prop_key', 'status_id')
                    ->select('journal_details.*')
                    ->orderBy('journals.created_on', 'DESC')
                    ->first();
                    
                if (!$latestStatusChange) {
                    return true; // 没有状态变更记录，直接返回成功
                }
                
                // 定义状态阶段映射
                $stageMapping = [
                    // 未进行阶段
                    'not_started' => [1, 11, 6], // 新建、未开始、重开
                    // 进行中阶段
                    'in_progress' => [2, 7, 9, 10, 12, 14], // 处理中、客户确认、待验证、待测试、进行中、待打包
                    // 已完成阶段
                    'completed' => [3, 4, 5, 13] // 已解决、已关闭、挂起、已完成
                ];
                
                $oldStatus = (int)$latestStatusChange->old_value;
                $newStatus = (int)$latestStatusChange->value;


                // 获取状态对应的阶段
                $oldStage = $this->getStatusStage($oldStatus, $stageMapping);
                $newStage = $this->getStatusStage($newStatus, $stageMapping);
                
                // 检查是否需要重置：从后面的阶段回到前面的阶段
                $stageOrder = ['not_started', 'in_progress', 'completed'];
                $oldStageIndex = array_search($oldStage, $stageOrder);
                $newStageIndex = array_search($newStage, $stageOrder);
                
                // 只有当从后面的阶段回到前面的阶段时才需要重置
                $needReset = $oldStageIndex !== false && $newStageIndex !== false && $newStageIndex < $oldStageIndex;
                
                if (!$needReset) {
                    return true; // 不需要重置，直接返回成功
                }
                
                Db::beginTransaction();
                
                try {
                    // 获取该issue下的所有检查清单
                    $checklists = $this->checklistModel::query()
                        ->where('issue_id', $issueId)
                        ->whereNull('deleted_at')
                        ->get();
                        
                    if ($checklists->isEmpty()) {
                        Db::commit();
                        return true; // 没有检查清单，直接返回成功
                    }
                    
                    // 重置所有检查清单项的状态为未完成
                    $checklistIds = $checklists->pluck('id')->toArray();
                    
                    $resetCount = $this->checklistItemModel::query()
                        ->whereIn('checklist_id', $checklistIds)
                        ->whereNull('deleted_at')
                        ->where('status', 1) // 只重置已完成的项
                        ->update(['status' => 0]); // 设置为未完成
                        
                    Db::commit();
                    
                    return true;
                    
                } catch (\Exception $e) {
                    Db::rollBack();
                    throw $e;
                }
                
            } catch (\Exception $e) {
                throw new AppException(StatusCode::ERR_SERVER, "重置检查清单项状态失败: " . $e->getMessage());
            }
        }

        /**
         * 获取状态对应的阶段
         * @param int $status 状态ID
         * @param array $stageMapping 阶段映射
         * @return string|null 阶段名称
         */
        private function getStatusStage(int $status, array $stageMapping): ?string
        {
            foreach ($stageMapping as $stage => $statuses) {
                if (in_array($status, $statuses)) {
                    return $stage;
                }
            }
            return null;
        }

        /**
         * 批量复制检查清单到新事项
         * @param int $sourceIssueId 源事项ID
         * @param int $targetIssueId 目标事项ID
         * @param int $targetProjectId 目标项目ID
         * @param bool $resetStatus 是否重置检查项状态
         * @param bool $useTransaction 是否使用事务（默认true，如果外部已有事务则设为false）
         * @return array 复制结果
         */
        public function copyChecklistsToIssue(int $sourceIssueId, int $targetIssueId, int $targetProjectId, bool $resetStatus = true, bool $useTransaction = true): array
        {
            if ($useTransaction) {
                Db::connection('tchip_redmine')->beginTransaction();
            }

            try {
                $userId = $this->auth->user()->getId();
                $copiedChecklists = [];

                // 获取源事项的所有检查清单（包含检查项）
                $sourceChecklists = $this->checklistModel::query()
                    ->where('issue_id', $sourceIssueId)
                    ->whereNull('deleted_at')
                    ->with(['items' => function($query) {
                        $query->whereNull('deleted_at')->orderBy('sort_order', 'ASC');
                    }])
                    ->get();

                if ($sourceChecklists->isEmpty()) {
                    if ($useTransaction) {
                        Db::connection('tchip_redmine')->commit();
                    }
                    return [
                        'success' => true,
                        'message' => '没有检查清单需要复制',
                        'copied_count' => 0,
                        'copied_checklists' => []
                    ];
                }

                foreach ($sourceChecklists as $sourceChecklist) {
                    // 创建新的检查清单
                    $newChecklist = $this->checklistModel::query()->create([
                        'project_id' => $targetProjectId,
                        'issue_id' => $targetIssueId,
                        'title' => $sourceChecklist->title,
                        'assignee' => $sourceChecklist->assignee,
                        'created_by' => $userId
                    ]);

                    // 批量准备检查清单项数据
                    $itemsData = [];
                    foreach ($sourceChecklist->items as $sourceItem) {
                        $itemsData[] = [
                            'checklist_id' => $newChecklist->id,
                            'item_title' => $sourceItem->item_title,
                            'status' => $resetStatus ? 0 : $sourceItem->status,
                            'remarks' => $sourceItem->remarks,
                            'sort_order' => $sourceItem->sort_order,
                            'custom_fields' => $sourceItem->custom_fields,
                            'created_by' => $userId,
                            'created_at' => now(),
                            'updated_at' => now()
                        ];
                    }

                    // 批量插入检查清单项
                    if (!empty($itemsData)) {
                        $this->checklistItemModel::query()->insert($itemsData);
                    }

                    $copiedChecklists[] = [
                        'id' => $newChecklist->id,
                        'title' => $newChecklist->title,
                        'items_count' => count($sourceChecklist->items)
                    ];
                }

                if ($useTransaction) {
                    Db::connection('tchip_redmine')->commit();
                }

                return [
                    'success' => true,
                    'message' => '检查清单复制成功',
                    'copied_count' => count($copiedChecklists),
                    'copied_checklists' => $copiedChecklists
                ];

            } catch (\Exception $e) {
                if ($useTransaction) {
                    Db::connection('tchip_redmine')->rollBack();
                }
                throw new AppException(StatusCode::ERR_SERVER, "复制检查清单失败: " . $e->getMessage());
            }
        }

        /**
         * 验证检查清单复制的完整性
         * @param int $sourceIssueId 源事项ID
         * @param int $targetIssueId 目标事项ID
         * @return array 验证结果
         */
        public function validateChecklistCopy(int $sourceIssueId, int $targetIssueId): array
        {
            try {
                // 获取源事项的检查清单统计
                $sourceStats = $this->checklistModel::query()
                    ->where('issue_id', $sourceIssueId)
                    ->whereNull('deleted_at')
                    ->selectRaw('COUNT(*) as checklist_count')
                    ->first();

                $sourceItemStats = $this->checklistItemModel::query()
                    ->join('checklists', 'checklist_items.checklist_id', '=', 'checklists.id')
                    ->where('checklists.issue_id', $sourceIssueId)
                    ->whereNull('checklists.deleted_at')
                    ->whereNull('checklist_items.deleted_at')
                    ->selectRaw('COUNT(*) as item_count')
                    ->first();

                // 获取目标事项的检查清单统计
                $targetStats = $this->checklistModel::query()
                    ->where('issue_id', $targetIssueId)
                    ->whereNull('deleted_at')
                    ->selectRaw('COUNT(*) as checklist_count')
                    ->first();

                $targetItemStats = $this->checklistItemModel::query()
                    ->join('checklists', 'checklist_items.checklist_id', '=', 'checklists.id')
                    ->where('checklists.issue_id', $targetIssueId)
                    ->whereNull('checklists.deleted_at')
                    ->whereNull('checklist_items.deleted_at')
                    ->selectRaw('COUNT(*) as item_count')
                    ->first();

                $isValid = ($sourceStats->checklist_count == $targetStats->checklist_count) &&
                          ($sourceItemStats->item_count == $targetItemStats->item_count);

                return [
                    'is_valid' => $isValid,
                    'source_checklists' => $sourceStats->checklist_count,
                    'target_checklists' => $targetStats->checklist_count,
                    'source_items' => $sourceItemStats->item_count,
                    'target_items' => $targetItemStats->item_count,
                    'message' => $isValid ? '检查清单复制验证通过' : '检查清单复制验证失败'
                ];

            } catch (\Exception $e) {
                return [
                    'is_valid' => false,
                    'error' => $e->getMessage(),
                    'message' => '检查清单复制验证出错'
                ];
            }
        }
    }