<?php

declare (strict_types=1);
namespace App\Model\Redmine\CheckList;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $project_id 
 * @property int $issue_id 
 * @property string $title 
 * @property int $assignee 
 * @property int $created_by 
 * @property string $created_at 
 * @property string $updated_at 
 * @property string $deleted_at 
 */
class ChecklistModel extends \App\Model\Redmine\RedmineBaseModel
{
    use SoftDeletes;
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'checklists';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'project_id',
        'issue_id',
        'title',
        'assignee',
        'created_by',
        'created_at',
        'updated_at',
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'project_id' => 'integer', 'issue_id' => 'integer', 'assignee' => 'integer', 'created_by' => 'integer'];

    /**
     * 关联检查清单项
     */
    public function items()
    {
        return $this->hasMany(ChecklistItemModel::class, 'checklist_id', 'id')
            ->whereNull('deleted_at')
            ->orderBy('sort_order', 'ASC');
    }

    /**
     * 关联事项
     */
    public function issue()
    {
        return $this->belongsTo(\App\Model\Redmine\IssueModel::class, 'issue_id', 'id');
    }

    /**
     * 关联项目
     */
    public function project()
    {
        return $this->belongsTo(\App\Model\Redmine\ProjectModel::class, 'project_id', 'id');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\Redmine\UserModel::class, 'created_by', 'id');
    }

    /**
     * 关联处理人
     */
    public function assigneeUser()
    {
        return $this->belongsTo(\App\Model\Redmine\UserModel::class, 'assignee', 'id');
    }
}