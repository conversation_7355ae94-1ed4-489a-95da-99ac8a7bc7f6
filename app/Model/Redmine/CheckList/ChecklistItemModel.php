<?php

declare (strict_types=1);
namespace App\Model\Redmine\CheckList;

use Hyperf\Database\Model\SoftDeletes;

/**
 * @property int $id 
 * @property int $checklist_id 
 * @property string $item_title 
 * @property int $status 
 * @property string $remarks 
 * @property int $sort_order 
 * @property string $custom_fields 
 * @property int $created_by 
 * @property string $created_at 
 * @property string $updated_at 
 * @property string $deleted_at 
 */

//自定义字段值集合，以 JSON 格式存储。例如 [
//  {
//    "name": "due_date",
//    "label": "截止日期",
//    "type": "date",
//    "required": false,
//    "value": "2025-06-10"
//  },
//  {
//      "name": "priority",
//    "label": "优先级",
//    "type": "number",
//    "required": false,
//    "value": 2
//  }
//]
class ChecklistItemModel extends \App\Model\Redmine\RedmineBaseModel
{
    use SoftDeletes;
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const DELETED_AT = 'deleted_at';
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'checklist_items';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'checklist_id',
        'item_title',
        'status',
        'remarks',
        'sort_order',
        'custom_fields',
        'created_by',
        'created_at',
        'updated_at',
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'checklist_id' => 'integer', 'status' => 'integer', 'sort_order' => 'integer', 'created_by' => 'integer', 'custom_fields' => 'json'];

    /**
     * 关联检查清单
     */
    public function checklist()
    {
        return $this->belongsTo(ChecklistModel::class, 'checklist_id', 'id');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(\App\Model\Redmine\UserModel::class, 'created_by', 'id');
    }
}