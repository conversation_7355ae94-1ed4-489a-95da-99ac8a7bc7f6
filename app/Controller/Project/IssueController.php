<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/6/22 上午10:12
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Controller\Project;

use App\Annotation\ControllerNameAnnotation;
use App\Core\Services\Project\IssueService;
use App\Core\Services\Project\IssueStatusesService;
use App\Core\Services\Project\ProjectService;
use App\Core\Services\Redmine\AttachmentService;
use App\Core\Services\Project\AttachmentService as proAttachmentService;
use App\Request\IdsRequest;
use App\Request\Project\Issue\DoMultiCreateRequest;
use App\Request\Project\NewIssueStatusRequest;
use App\Request\Project\Issue\IssueRelationRequest;
use App\Request\Project\Issue\UnIssueRelationRequest;
use App\Request\Project\TrackerIdRequest;
use Hyperf\Di\Annotation\Inject;
use Hyperf\HttpServer\Annotation\AutoController;
use Hyperf\HttpServer\Annotation\Middleware;
use App\Middleware\AuthMiddleware;
use Psr\Http\Message\ResponseInterface;

/**
 * @ControllerNameAnnotation("任务管理")
 * @AutoController(prefix="/project/issue/index")
 * @Middleware(AuthMiddleware::class)
 */
class IssueController extends \App\Controller\BaseController
{

    /**
     * @Inject()
     * @var IssueService
     */
    protected $service;

    /**
     * @Inject()
     * @var AttachmentService
     */
    protected $attachmentService;

    /**
     * @Inject()
     * @var proAttachmentService
     */
    protected $proAttachmentService;

    public function getList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * @ControllerNameAnnotation("事项关联列表")
     * @return ResponseInterface
     */
    public function issueRelationsList()
    {
        $issueId = $this->request->input('issue_id');
        $tracker_id = $this->request->input('tracker_id') ?? null;
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        //if ($tracker_id) {
        //    $filter = array_merge($filter, ['tracker_id' => $tracker_id]);
        //}
        $issueId = $filter['issue_id'];
        unset($filter['issue_id']);
        $result = $this->service->issueRelationsList($issueId, $filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * 任务详情信息
     * @return ResponseInterface
     */
    public function overView(): ResponseInterface
    {
        $id = $this->request->input('id',0);
        $result = $this->service->getOverView($id);
        return $this->response->success($result);
    }

    /**
     * 获取平铺的子事项列表
     * @return ResponseInterface
     */
    public function getAllChild()
    {
        $id = $this->request->input('id',0);
        list($filter, $op, $sort, $order) = $this->getParams();
        $result = $this->service->getAllChild($id, 0, $filter, $op, $sort, $order);
        return $this->response->success($result);
    }

    /**
     * 修改事项所属项目
     */
    public function changeIssueProject()
    {
        $id = $this->request->input('id');
        $projectId = $this->request->input('projectId');
        $versionId = $this->request->input('versionId');
        $result = $this->service->changeIssueProject($id,$projectId,$versionId);
        return $this->response->success($result);
    }

    /**
     * 批量迁移事项至其他项目
     */
    public function batchMigrateIssueProject()
    {
        $issueIds = $this->request->input('issueIds', []);
        $projectId = $this->request->input('projectId');
        $versionId = $this->request->input('versionId');
        $unlinkChildrenIds = $this->request->input('unlinkChildrenIds', []);

        if (empty($issueIds) || !$projectId) {
            return $this->response->error('','参数错误');
        }

        try {
            $result = $this->service->batchMigrateIssueProject($issueIds, $projectId, $versionId, $unlinkChildrenIds);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('','批量迁移失败');
        }
    }

    /**
     * 批量复制事项
     */
    public function batchCopyIssue()
    {
        $params = $this->request->all();

        // 验证必要参数
        if (empty($params['target_project_id']) || empty($params['issues'])) {
            return $this->response->error('', '参数错误：缺少目标项目ID或事项列表');
        }

        // 验证事项列表格式
        if (!is_array($params['issues'])) {
            return $this->response->error('', '参数错误：事项列表格式不正确1');
        }

        try {
            $result = $this->service->batchCopyIssue($params);
            return $this->response->success($result);
        } catch (\Exception $e) {
            return $this->response->error('', '批量复制失败：' . $e->getMessage());
        }
    }



    /**
     * @return ResponseInterface
     */
    public function doEdit(): ResponseInterface
    {
        $id = $this->request->input('id',0);
        $params = $this->request->all();
        unset($params['id']);
        $result = $this->service->doEdit($id, $params);
        return $this->response->success($result);
    }

    /**
     * @return ResponseInterface
     */
    public function doMultiCreate(DoMultiCreateRequest $request): ResponseInterface
    {
        $validated = $request->validated();
        $params = $this->request->all();
        $result = $this->service->doMultiCreate($validated['project_id'], $validated['tracker_id'], $params);
        return $this->response->success($result);
    }

    /**
     * 关注任务
     * @return ResponseInterface
     */
    public function doWatchers(): ResponseInterface
    {
        $id = $this->request->input('id',0);
        $watcher = $this->request->input('watcher',0);
        $result = $this->service->doWatchers($id, $watcher);
        return $this->response->success($result);
    }

    /**
     * 关注任务
     * @return ResponseInterface
     */
    public function doWatchersMulti(IdsRequest $request): ResponseInterface
    {
        $validated = $request->validated();
        $watcher = $this->request->input('watcher',0);
        $result = $this->service->doWatchersMulti($validated['ids'], $watcher);
        return $this->response->success($result);
    }

    /**
     * 上传附件
     * @return ResponseInterface
     */
    public function uploadAttachment(): ResponseInterface
    {
        $file = $this->request->file('file');
        $result = $this->attachmentService->upload($file->getClientFilename(), $file->getRealPath());
        return $this->response->success($result);
    }

    public function deleteAttachment()
    {
        $id = $this->request->input('id');
        // $result = $this->attachmentService->delete($id);
        $result = $this->proAttachmentService->doDelete($id);
        return $this->response->success($result);
    }

    public function getIssueStatus()
    {
        $result = $this->service->getIssueStatus();
        return $this->response->success($result);
    }

    public function getNewIssueStatus(NewIssueStatusRequest $newIssueStatusRequest)
    {
        $validated = $newIssueStatusRequest->validated();
        $statusId = $this->request->input('status_id');
        $authorId = $this->request->input('author_id');
        $assignId = $this->request->input('assigned_to_id');
        $result = $this->service->getNewIssueStatus(null, $validated['project_id'], $validated['tracker_id'], $statusId ?? 0, $authorId, $assignId);
        return $this->response->success($result);
    }

    public function reTrackerIssueStatus()
    {
        $issueId = $this->request->input('issue_id');
        $result = $this->service->reTrackerIssueStatus($issueId);
        return $this->response->success($result);
    }

    public function getTrackersStatusList()
    {
        $validated = make(TrackerIdRequest::class)->validated();
        $result = make(IssueStatusesService::class)->getTrackerStatusList($validated['tracker_id']);
        return $this->response->success($result);
    }

    /**
     * 同步事项处理人到多人指派
     * @return void
     */
    public function assignToIdMigrations()
    {
        $this->service->assignToIdMigrations();
        return $this->response->success();
    }

    public function addIssueRelation(IssueRelationRequest $request)
    {
        $validated = $request->validated();
        $issueId   = $validated['id'];
        unset($validated['id']);
        $result = make(\App\Core\Services\Redmine\IssueService::class)->addIssueRelation($issueId, $validated);
        return $this->response->success($result);
    }

    public function delIssueRelation(UnIssueRelationRequest $request)
    {
        $validated = $request->validated();
        $result = make(\App\Core\Services\Redmine\IssueService::class)->delIssueRelation($validated['id']);
        return $this->response->success($result);
    }

    /**
     *
     * @return ResponseInterface
     */
    public function myWorkCount()
    {
        $user_id = $this->request->input('user_id');
        $result = $this->service->myWorkCount($user_id);
        return $this->response->success($result);
    }

    public function getCategoryList()
    {
        list($filter, $op, $sort, $order, $limit) = $this->getParams();
        $result = $this->service->getCategoryList($filter, $op, $sort, $order, $limit);
        return $this->response->success($result);
    }

    /**
     * * @ControllerNameAnnotation("同步事项处理人到issue_assigned表")
     * @return ResponseInterface
     */
    public function syncIssueAssigned()
    {
        return $this->response->success($this->service->syncIssueAssigned());
    }

    /**
     * @ControllerNameAnnotation("获取参与的项目的版本")
     * @return ResponseInterface
     */
    public function getIssueVersions()
    {
        $result = $this->service->getIssueVersions();
        return $this->response->success($result);
    }

}