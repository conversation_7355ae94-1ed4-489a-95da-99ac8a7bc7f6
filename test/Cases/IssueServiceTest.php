<?php

declare(strict_types=1);

namespace HyperfTest\Cases;

use HyperfTest\HttpTestCase;
use App\Core\Services\Project\IssueService;
use Hyperf\Testing\TestCase;

/**
 * @internal
 * @coversNothing
 */
class IssueServiceTest extends TestCase
{
    protected IssueService $issueService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->issueService = make(IssueService::class);
    }

    /**
     * 测试getAllChildIssues方法是否能正确获取子事项
     */
    public function testGetAllChildIssues()
    {
        // 这是一个基本的方法测试，确保方法存在且可调用
        $result = $this->issueService->getAllChildIssues([]);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取自定义字段的方法
     */
    public function testGetIssuesCustomFields()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesCustomFields');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取关注人的方法
     */
    public function testGetIssuesWatchers()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesWatchers');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取指派用户的方法
     */
    public function testGetIssuesAssignedUsers()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesAssignedUsers');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试batchCopyIssue方法的基本结构
     * 注意：这个测试不会实际执行复制操作，只是验证方法结构
     */
    public function testBatchCopyIssueStructure()
    {
        // 验证方法存在
        $this->assertTrue(method_exists($this->issueService, 'batchCopyIssue'));
        
        // 验证方法是公开的
        $reflection = new \ReflectionMethod($this->issueService, 'batchCopyIssue');
        $this->assertTrue($reflection->isPublic());
    }

    /**
     * 测试子事项去重逻辑
     * 验证当用户勾选的事项中包含子事项时，不会重复复制
     */
    public function testChildIssueDeduplication()
    {
        // 模拟事项数据结构
        $issues = [
            ['id' => 1, 'subject' => '父事项A'],
            ['id' => 2, 'subject' => '子事项B'], // 用户明确勾选的子事项
        ];
        
        // 模拟getAllChildIssues返回的数据（包含用户已勾选的子事项）
        $mockChildIssues = [
            ['id' => 2, 'subject' => '子事项B'], // 重复的子事项
            ['id' => 3, 'subject' => '子事项C'],
            ['id' => 4, 'subject' => '孙事项D'],
        ];
        
        // 验证去重逻辑
        $selectedIssueIds = array_column($issues, 'id'); // [1, 2]
        $filteredChildIssues = array_filter($mockChildIssues, function($childIssue) use ($selectedIssueIds) {
            return !in_array($childIssue['id'], $selectedIssueIds);
        });
        
        // 断言：过滤后应该只剩下ID为3和4的子事项
        $this->assertCount(2, $filteredChildIssues);
        $filteredIds = array_column($filteredChildIssues, 'id');
        $this->assertContains(3, $filteredIds);
        $this->assertContains(4, $filteredIds);
        $this->assertNotContains(2, $filteredIds); // 确保重复的子事项被过滤掉
    }
}
