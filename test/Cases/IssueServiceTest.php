<?php

declare(strict_types=1);

namespace HyperfTest\Cases;

use HyperfTest\HttpTestCase;
use App\Core\Services\Project\IssueService;
use Hyperf\Testing\TestCase;

/**
 * @internal
 * @coversNothing
 */
class IssueServiceTest extends TestCase
{
    protected IssueService $issueService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->issueService = make(IssueService::class);
    }

    /**
     * 测试getAllChildIssues方法是否能正确获取子事项
     */
    public function testGetAllChildIssues()
    {
        // 这是一个基本的方法测试，确保方法存在且可调用
        $result = $this->issueService->getAllChildIssues([]);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取自定义字段的方法
     */
    public function testGetIssuesCustomFields()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesCustomFields');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取关注人的方法
     */
    public function testGetIssuesWatchers()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesWatchers');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试批量获取指派用户的方法
     */
    public function testGetIssuesAssignedUsers()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getIssuesAssignedUsers');
        $method->setAccessible(true);
        
        $result = $method->invoke($this->issueService, []);
        $this->assertIsArray($result);
    }

    /**
     * 测试batchCopyIssue方法的基本结构
     * 注意：这个测试不会实际执行复制操作，只是验证方法结构
     */
    public function testBatchCopyIssueStructure()
    {
        // 验证方法存在
        $this->assertTrue(method_exists($this->issueService, 'batchCopyIssue'));
        
        // 验证方法是公开的
        $reflection = new \ReflectionMethod($this->issueService, 'batchCopyIssue');
        $this->assertTrue($reflection->isPublic());
    }

    /**
     * 测试子事项去重逻辑
     * 验证当用户勾选的事项中包含子事项时，不会重复复制
     */
    public function testChildIssueDeduplication()
    {
        // 模拟事项数据结构
        $issues = [
            ['id' => 1, 'subject' => '父事项A'],
            ['id' => 2, 'subject' => '子事项B'], // 用户明确勾选的子事项
        ];
        
        // 模拟getAllChildIssues返回的数据（包含用户已勾选的子事项）
        $mockChildIssues = [
            ['id' => 2, 'subject' => '子事项B'], // 重复的子事项
            ['id' => 3, 'subject' => '子事项C'],
            ['id' => 4, 'subject' => '孙事项D'],
        ];
        
        // 验证去重逻辑
        $selectedIssueIds = array_column($issues, 'id'); // [1, 2]
        $filteredChildIssues = array_filter($mockChildIssues, function($childIssue) use ($selectedIssueIds) {
            return !in_array($childIssue['id'], $selectedIssueIds);
        });
        
        // 断言：过滤后应该只剩下ID为3和4的子事项
        $this->assertCount(2, $filteredChildIssues);
        $filteredIds = array_column($filteredChildIssues, 'id');
        $this->assertContains(3, $filteredIds);
        $this->assertContains(4, $filteredIds);
        $this->assertNotContains(2, $filteredIds); // 确保重复的子事项被过滤掉
    }

    /**
     * 测试父事项日期获取逻辑
     * 验证能正确获取父事项的日期信息用于子事项同步
     */
    public function testGetParentDatesForChildIssues()
    {
        // 使用反射来测试私有方法
        $reflection = new \ReflectionClass($this->issueService);
        $method = $reflection->getMethod('getParentDatesForChildIssues');
        $method->setAccessible(true);
        
        // 模拟父事项数据
        $parentIssues = [
            [
                'id' => 1, 
                'subject' => '父事项A',
                'start_date' => '2025-01-01',
                'due_date' => '2025-01-31'
            ],
            [
                'id' => 2, 
                'subject' => '父事项B',
                'start_date' => '2025-02-01',
                'due_date' => '2025-02-28'
            ]
        ];
        
        // 模拟子事项数据
        $childIssues = [
            ['id' => 3, 'parent_id' => 1, 'subject' => '子事项C'],
            ['id' => 4, 'parent_id' => 2, 'subject' => '子事项D'],
        ];
        
        $result = $method->invoke($this->issueService, $parentIssues, $childIssues);
        
        // 断言：应该返回按父事项ID分组的日期信息
        $this->assertIsArray($result);
        $this->assertArrayHasKey(1, $result);
        $this->assertArrayHasKey(2, $result);
        $this->assertEquals('2025-01-01', $result[1]['start_date']);
        $this->assertEquals('2025-01-31', $result[1]['due_date']);
        $this->assertEquals('2025-02-01', $result[2]['start_date']);
        $this->assertEquals('2025-02-28', $result[2]['due_date']);
    }

    /**
     * 测试子事项状态和日期设置
     * 验证搜寻到的子事项状态被设置为新建状态，日期同步父事项
     */
    public function testChildIssueStatusAndDateSync()
    {
        // 验证常量存在
        $this->assertEquals(1, \App\Constants\IssueCode::ISSUE_STATUS_CREATE_NEW);
        
        // 模拟子事项格式化逻辑
        $childIssue = [
            'id' => 3,
            'subject' => '子事项C',
            'tracker_id' => 1,
            'status_id' => 2, // 原状态
            'priority_id' => 1,
            'category_id' => 0,
            'fixed_version_id' => null,
            'assigned_to_id' => null,
            'start_date' => '2024-12-01', // 原开始日期
            'due_date' => '2024-12-31',   // 原完成日期
            'class_id' => 0,
            'parent_id' => 1,
        ];
        
        $parentDate = [
            'start_date' => '2025-01-01',
            'due_date' => '2025-01-31'
        ];
        
        // 模拟格式化后的子事项
        $formattedChildIssue = [
            'id' => $childIssue['id'],
            'subject' => $childIssue['subject'],
            'tracker_id' => $childIssue['tracker_id'],
            'status_id' => \App\Constants\IssueCode::ISSUE_STATUS_CREATE_NEW, // 应该被设置为新建状态
            'priority_id' => $childIssue['priority_id'],
            'category_id' => $childIssue['category_id'],
            'fixed_version_id' => $childIssue['fixed_version_id'],
            'assigned_to_id' => $childIssue['assigned_to_id'],
            'start_date' => $parentDate['start_date'], // 应该同步父事项日期
            'due_date' => $parentDate['due_date'],     // 应该同步父事项日期
            'class_id' => $childIssue['class_id'],
            'parent_id' => $childIssue['parent_id'],
        ];
        
        // 断言：状态应该被设置为新建状态
        $this->assertEquals(1, $formattedChildIssue['status_id']);
        
        // 断言：日期应该同步父事项
        $this->assertEquals('2025-01-01', $formattedChildIssue['start_date']);
        $this->assertEquals('2025-01-31', $formattedChildIssue['due_date']);
        
        // 断言：其他字段保持不变
        $this->assertEquals($childIssue['subject'], $formattedChildIssue['subject']);
        $this->assertEquals($childIssue['tracker_id'], $formattedChildIssue['tracker_id']);
        $this->assertEquals($childIssue['parent_id'], $formattedChildIssue['parent_id']);
    }
}
