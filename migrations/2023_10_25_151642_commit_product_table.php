<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitProductTable extends Migration
{

    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('product', function (Blueprint $table) {
        //     if (Schema::hasColumn('product', 'hard_handler_uid')) {
        //         $table->string('hard_handler_uid', '100')->change();
        //     }
        //
        //     if (Schema::hasColumn('product', 'soft_handler_linux_uid')) {
        //         $table->string('soft_handler_linux_uid', '100')->comment('软件负责人')->change();
        //     }
        //
        //     if (Schema::hasColumn('product', 'soft_handler_android_uid')) {
        //         $table->dropColumn('soft_handler_android_uid');
        //     }
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product', function (Blueprint $table) {
            //
        });
    }
}
