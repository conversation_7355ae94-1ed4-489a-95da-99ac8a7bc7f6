<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateErpProductTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('erp_product')) {
            Schema::create('erp_product', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('prod_name', 64)->comment('产品名称');
                $table->string('prod_spec', 128)->comment('规格');
                $table->string('prod_code', 32)->comment('料号');
                $table->smallInteger('prod_pmpc')->comment('pmpccode');
                $table->json('silk')->nullable()->comment('丝印');
                $table->json('wrong_silk')->nullable()->comment('错误丝印');
                $table->longText('silk_remark')->nullable()->comment('错误丝印');
                $table->tinyInteger('status')->default(1)->comment('状态');
                $table->tinyInteger('is_exempt')->default(0)->comment('是否免检');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('erp_product');
    }
}
