<?php

/*
 * @Description: 天启文化数据表迁移(分类: regulation-制度流程, announcement-公告通知, training-培训文档, office-办公文档, newcomer-新人报到)
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-18 10:53:03
 * @LastEditors: 张权江
 * @LastEditTime: 2025-03-18 11:22:47
 */
use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateCultureTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('culture')) {
            Schema::create('culture', function (Blueprint $table) {
                // 基本信息
                $table->integerIncrements('id')->comment('文档ID');
                $table->string('title')->default('')->comment('标题');
                // 使用英文代替中文作为分类标识
                $table->enum('category', ['regulation', 'announcement', 'training', 'office', 'newcomer'])->comment('分类: regulation-制度流程, announcement-公告通知, training-培训文档, office-办公文档, newcomer-新人报到');
                $table->text('content')->nullable()->comment('内容');
                $table->string('attachment')->nullable()->comment('附件');
                $table->dateTime('published_at')->nullable()->comment('发布时间');
                $table->string('publisher')->default('')->comment('发布人');

                // 排序与置顶
                $table->integer('sort_order')->default(0)->comment('排序');
                $table->boolean('is_top')->default(false)->comment('是否置顶');

                // 时间戳
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();

                // 索引
                $table->index('category');
                $table->index('published_at');
                $table->index('is_top');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('culture');
    }
}