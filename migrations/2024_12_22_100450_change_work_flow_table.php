<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeWorkFlowTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('work_flow')) {
            Schema::table('work_flow', function (Blueprint $table) {
                if (!Schema::hasColumn('work_flow', 'completeness_status')) {
                    $table->json('pre_operation')->nullable()->comment('状态修改的前置操作');
                }

                if (!Schema::hasColumn('work_flow', 'is_reverse')) {
                    $table->tinyInteger('is_reverse')->default(0)->comment('是否为逆向流程{0:否,1:是}');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_flow', function (Blueprint $table) {
            //
        });
    }
}
