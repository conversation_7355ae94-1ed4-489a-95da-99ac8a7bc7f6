<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitFieldOaQcTable extends Migration
{
 /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_qc', function (Blueprint $table) {
            // 1. 抽样计划(单选)：加强、正常(默认值)、减量
            $table->string('sampling_plan', 20)->default('normal')
                  ->comment('抽样计划：enhanced(加强)、normal(正常)、reduced(减量)');
            
            // 2. 缺陷分类(复选)及数量值
            $table->json('defect_categories')->nullable()->comment('缺陷分类及数量');
            
            // 3. 外观检验说明、功能检验说明
            $table->text('appearance_inspection_remark')->nullable()->comment('外观检验说明');
            $table->text('function_inspection_remark')->nullable()->comment('功能检验说明');
            
            // 4. 附件
            $table->json('attachments')->nullable()->comment('附件信息，JSON 格式');
            
            // 5. 关注者
            $table->json('followers')->nullable()->comment('关注者ID列表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('oa_qc', function (Blueprint $table) {
            $table->dropColumn([
                'sampling_plan', 
                'defect_categories',
                'appearance_inspection_remark',
                'function_inspection_remark',
                'attachments',
                'followers'
            ]);
        });
    }
}
