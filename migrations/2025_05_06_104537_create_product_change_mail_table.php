<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductChangeMailTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_change_mail')) {
            Schema::create('product_change_mail', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->unsignedInteger('change_record_id')->comment('记录id')->index();
                $table->string('mail_title',500)->default('')->comment('标题');
                $table->text('mail_content')->nullable()->comment('发送内容');
                $table->text('mail_content_html')->nullable()->comment('发送内容');
                $table->json('client_ids')->nullable()->comment('发送客户');

                $table->unsignedInteger('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_change_mail', function (Blueprint $table) {
            //
        });
    }
}
