<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductChangeMailDetailTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_change_mail_detail')) {
            Schema::create('product_change_mail_detail', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->unsignedInteger('change_record_id')->default(0)->comment('变更记录id')->index();
                $table->unsignedInteger('mail_id')->default(0)->comment('发送id')->index();
                $table->unsignedInteger('client_id')->default(0)->comment('发送客户')->index();
                $table->string('client_company')->default('')->comment('发送客户公司')->index();
                $table->string('client_name')->default('')->comment('发送客户名称')->index();
                $table->string('client_email')->default('')->comment('发送客户邮箱')->index();
                $table->tinyInteger('send_status')->default(1)->comment('发送状态{1:成功,2:失败}');
                $table->text('send_result')->nullable()->comment('发送结果');

                $table->unsignedInteger('created_by')->nullable()->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_change_mail_detail', function (Blueprint $table) {
            //
        });
    }
}
