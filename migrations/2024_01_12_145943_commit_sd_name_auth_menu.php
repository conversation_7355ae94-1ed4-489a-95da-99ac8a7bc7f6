<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitSdNameAuthMenu extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('auth_menu')) {
            Schema::table('auth_menu', function (Blueprint $table) {
                if (!Schema::hasColumn('auth_menu', 'sd_name')) {
                    $table->string('sd_name', 100)->default('')->comment('简写名称');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
