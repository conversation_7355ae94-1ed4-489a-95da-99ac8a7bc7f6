<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
use Hyperf\Database\Migrations\Migration;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Schema\Schema;

class CommitOaRecordsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('oa_records', function (Blueprint $table) {
            // 添加其他字段
            if (! Schema::hasColumn('oa_records', 'asset_id')) {
                $table->unsignedBigInteger('asset_id')->nullable()->comment('资产ID');
            }
            if (! Schema::hasColumn('oa_records', 'operation_time')) {
                $table->date('operation_time')->nullable()->comment('操作时间');
            }
            if (! Schema::hasColumn('oa_records', 'operation_desc')) {
                $table->text('operation_desc')->nullable()->comment('描述');
            }
            if (! Schema::hasColumn('oa_records', 'type')) {
                $table->tinyInteger('type')->nullable()->comment('type=1代表维修，type=2时是报废,type=3为转移');
            }
            if (! Schema::hasColumn('oa_records', 'status')) {
                $table->tinyInteger('status')->nullable()->comment("1=>‘维修处理中'，2=>‘维修已完成'3=>‘报废处理中'，4=>‘报废已完成'，5=>‘转移处理中'，6=>‘转移已完成'");
            }
            if (! Schema::hasColumn('oa_records', 'user_id')) {
                $table->unsignedBigInteger('user_id')->nullable()->comment('User ID');
            }
            if (! Schema::hasColumn('oa_records', 'received_user_id')) {
                $table->integer('received_user_id')->nullable()->comment('Received User ID');
            }
            if (! Schema::hasColumn('oa_records', 'receiver_depart_id')) {
                $table->integer('receiver_depart_id')->nullable()->comment('Receiver Department ID');
            }
            if (! Schema::hasColumn('oa_records', 'approval_status')) {
                $table->tinyInteger('approval_status')->nullable()->comment('审批状态：1-审批通过, 其他为未通过');
            }
            if (! Schema::hasColumn('oa_records', 'cost')) {
                $table->decimal('cost', 10, 2)->nullable()->comment('操作所需花费');
            }
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
        });
    }
}
