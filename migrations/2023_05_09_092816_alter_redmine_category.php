<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class AlterRedmineCategory extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Schema::table('category', function (Blueprint $table) {
        //     $table->string('name', 128)->change();
        //     $table->string('keywords', 128)->change();
        // });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('category', function (Blueprint $table) {

        });
    }
}
