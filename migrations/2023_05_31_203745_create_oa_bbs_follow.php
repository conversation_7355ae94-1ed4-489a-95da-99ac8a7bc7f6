<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateOaBbsFollow extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('oa_bbs_follow')) {
            Schema::create('oa_bbs_follow', function (Blueprint $table) {
                $table->bigIncrements('id')->primary();
                $table->unsignedTinyInteger('source')
                    ->comment('来源：1-Firefly中文论坛，2-Firefly英文论坛');
                $table->unsignedInteger('tid')->comment('帖子ID');
                $table->string('subject', '200')->comment('帖子标题');
                $table->unsignedInteger('authorid')->comment('作者');
                $table->string('author', '100');
                $table->unsignedInteger('follower')->comment('跟进人员ID');
                $table->text('last_reply')->comment('最后回复内容');
                $table->dateTime('last_reply_time')->comment('最后回复时间');
                $table->tinyInteger('status')->comment('状态：0-跟进中，1-已解决，2-已关闭');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('oa_bbs_follow');
    }
}
