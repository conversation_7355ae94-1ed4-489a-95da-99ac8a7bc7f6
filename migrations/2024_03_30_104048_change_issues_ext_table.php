<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class ChangeIssuesExtTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issues_ext')) {
            Schema::table('issues_ext', function (Blueprint $table) {
                if (Schema::hasColumn('issues_ext', 'release_version_id')) {
                    $table->integer('release_version_id')->default(0)->change();
                }
            });

        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
