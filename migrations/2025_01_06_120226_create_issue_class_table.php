<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateIssueClassTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('issue_class', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('parent_id')->default(0)->comment('父级分类ID');
            $table->string('name', 255)->default('')->comment('事项分类名称');
            $table->string('path', 255)->default('')->comment('路径,格式如：1-23-44');
            $table->integer('project_id')->default(0)->comment('项目id');
            $table->text('description')->nullable()->comment('分类描述');
            //$table->dateTime('created_at')->nullable()->comment('创建时间');
            //$table->dateTime('updated_at')->nullable()->comment('更新时间');
            $table->timestamps();
            //$table->dateTime('deleted_at')->nullable()->comment('删除时间');
            $table->softDeletes();
            $table->index('project_id');
            $table->index('parent_id');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('issue_class');
    }
}
