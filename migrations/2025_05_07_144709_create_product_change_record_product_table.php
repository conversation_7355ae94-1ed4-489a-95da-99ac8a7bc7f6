<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateProductChangeRecordProductTable extends Migration
{
    /**
     * 指定数据库连接
     */
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_change_record_product')) {
            Schema::create('product_change_record_product', function (Blueprint $table) {
                $table->integerIncrements('id');
                $table->unsignedInteger('change_record_id')->default(0)->comment('变更记录id')->index();
                $table->string('product_name',500)->default('')->comment('ERP产品名称');
                $table->char('product_code',50)->default('')->comment('ERP产品料号');
                $table->string('product_spec',500)->default('')->comment('ERP产品配置');

                $table->unsignedInteger('created_by')->default(0)->comment('创建人');
                $table->dateTime('created_at')->nullable();
                $table->dateTime('updated_at')->nullable();
                $table->dateTime('deleted_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_change_record_product', function (Blueprint $table) {
            //
        });
    }
}
