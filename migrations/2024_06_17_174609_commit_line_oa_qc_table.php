<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CommitLineOaQcTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('oa_qc')) {
            Schema::table('oa_qc', function (Blueprint $table) {
                if (!Schema::hasColumn('oa_qc', 'line')) {
                    $table->smallInteger('line')->default(0)->comment('入库行');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('', function (Blueprint $table) {
            //
        });
    }
}
