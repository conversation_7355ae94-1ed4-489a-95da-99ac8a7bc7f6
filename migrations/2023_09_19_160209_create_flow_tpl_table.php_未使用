<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFlowTplTable extends Migration
{
    protected $connection = 'tchip_redmine';
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('flow_tpl')) {
            Schema::create('flow_tpl', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name', 64);
                $table->char('flow_type', 16)->comment('程流类型，issue,project');
                $table->integer('project_id')->comment('项目ID');
                $table->longText('tpl')->nullable()->comment('流程结构');
                $table->dateTime('created_at');
                $table->dateTime('updated_at');
                $table->dateTime('deleted_at')->nullable();
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flow_tpl');
    }
}
