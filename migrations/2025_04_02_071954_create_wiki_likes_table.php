<?php

    use Hyperf\Database\Schema\Schema;
    use Hyperf\Database\Schema\Blueprint;
    use Hyperf\Database\Migrations\Migration;

    class CreateWikiLikesTable extends Migration
    {
        /**
         * Run the migrations.
         */
        public function up(): void
        {
            if (!Schema::hasTable('wiki_likes')) {
                Schema::create('wiki_likes', function (Blueprint $table) {
                    // 主键
                    $table->bigIncrements('like_id')->comment('点赞ID');

                    // 字段
                    $table->bigInteger('doc_id')->comment('文档ID');
                    $table->bigInteger('user_id')->comment('用户ID');
                    $table->dateTime('deleted_at')->nullable()->comment('软删除时间');
                    $table->dateTime('created_at')->comment('创建时间');
                    $table->dateTime('updated_at')->comment('更新时间');

                    // 索引
                    $table->index('doc_id');
                    $table->index('user_id');
                });
            }
        }

        /**
         * Reverse the migrations.
         */
        public function down(): void
        {
            Schema::dropIfExists('wiki_likes');
        }
    }
