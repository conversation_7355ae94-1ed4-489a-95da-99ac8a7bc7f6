<?php
/*
 * @Description: 
 * @Version: 1.0
 * @Autor: 张权江
 * @Date: 2025-03-21 13:52:25
 * @LastEditors: 张权江
 * @LastEditTime: 2025-03-21 18:00:37
 */

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateCompanyPhilosophyTables extends Migration
{
    /**
     * 运行迁移
     */
    public function up(): void
    {
        // 创建公司理念主表
        Schema::create('philosophy', function (Blueprint $table) {
            $table->increments('id')->comment('主键ID');
            $table->string('title', 255)->comment('标题');
            $table->text('content')->nullable()->comment('内容');
            $table->string('type', 50)->default('core')->comment('文章类型');
            $table->integer('sort_order')->default(0)->comment('排序');
            $table->tinyInteger('status')->default(1)->comment('状态：0-禁用，1-启用');
            $table->integer('publisher')->unsigned()->nullable()->comment('创建人ID');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index('type', 'idx_type');
            $table->index('status', 'idx_status');
        });

        // 创建公司理念修改记录表
        Schema::create('philosophy_record', function (Blueprint $table) {
            $table->increments('id')->comment('记录ID');
            $table->integer('philosophy_id')->unsigned()->comment('关联的内容ID');
            $table->text('content')->nullable()->comment('变更后的内容');
            $table->integer('publisher')->unsigned()->comment('操作人ID');
            $table->string('change_type', 20)->default('update')->comment('变更类型：create-创建, update-更新, delete-删除');
            $table->string('change_note', 255)->nullable()->comment('变更说明');
            $table->timestamps();
            
            $table->index('philosophy_id', 'idx_philosophy_id');
            $table->index('publisher', 'idx_publisher');
        });
    }

    /**
     * 回滚迁移
     */
    public function down(): void
    {
        Schema::dropIfExists('philosophy_record');
        Schema::dropIfExists('philosophy');
    }
}