# 日志记录格式统一修改

## 概述

根据参考格式 `Log::get('system', 'error')->info()` 统一修改了 `app/Core/Services/Project/IssueService.php` 中所有的日志记录方式。

## 修改内容

### 修改前的格式
```php
// 不统一的格式
Log::get('system')->error()
Log::get('system')->info()
Log::get('system')->warning()
Log::get('system', 'system')->info()
```

### 修改后的格式
```php
// 统一的格式
Log::get('system', 'error')->error()
Log::get('system', 'info')->info()
Log::get('system', 'warning')->warning()
```

## 具体修改位置

### 1. 复制事项失败日志
**位置**: 第1674行
**修改前**: `Log::get('system')->error()`
**修改后**: `Log::get('system', 'error')->error()`

### 2. 更新父子关系失败日志
**位置**: 第1692行
**修改前**: `Log::get('system')->error()`
**修改后**: `Log::get('system', 'error')->error()`

### 3. 检查清单复制成功日志
**位置**: 第1940行
**修改前**: `Log::get('system')->info()`
**修改后**: `Log::get('system', 'info')->info()`

### 4. 检查清单复制验证失败日志
**位置**: 第1949行
**修改前**: `Log::get('system')->warning()`
**修改后**: `Log::get('system', 'warning')->warning()`

### 5. 复制检查清单失败日志
**位置**: 第1960行
**修改前**: `Log::get('system')->error()`
**修改后**: `Log::get('system', 'error')->error()`

### 6. 创建事项关联关系失败日志
**位置**: 第2039行
**修改前**: `Log::get('system')->error()`
**修改后**: `Log::get('system', 'error')->error()`

### 7. 同步指派人开始日志
**位置**: 第3241行
**修改前**: `Log::get('system', 'system')->info()`
**修改后**: `Log::get('system', 'info')->info()`

### 8. 同步指派人进度日志
**位置**: 第3251行
**修改前**: `Log::get('system', 'system')->info()`
**修改后**: `Log::get('system', 'info')->info()`

## 日志级别说明

- **error**: 用于记录错误信息，如复制失败、关系创建失败等
- **warning**: 用于记录警告信息，如验证失败等
- **info**: 用于记录一般信息，如操作成功、进度信息等

## 格式规范

统一使用以下格式：
```php
Log::get('system', '{level}')->{level}('消息内容', [
    'key1' => 'value1',
    'key2' => 'value2',
    // 其他上下文数据
]);
```

其中 `{level}` 可以是：
- `error`
- `warning` 
- `info`
- `debug`

## 优势

1. **格式统一**: 所有日志记录使用相同的格式
2. **分类清晰**: 通过第二个参数明确指定日志级别
3. **便于管理**: 可以根据级别进行日志过滤和管理
4. **符合规范**: 与项目中其他地方的日志格式保持一致
