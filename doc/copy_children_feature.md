# 事项复制子事项功能实现

## 概述

本次修改实现了真正的`copy_children`功能，当用户在批量复制事项时选择"复制子事项"选项，系统将递归查找并复制所有子事项及其子孙事项。

## 修改文件

- `app/Core/Services/Project/IssueService.php`

## 主要功能

### 1. 递归子事项查找
- 使用`getAllChildIssues()`方法递归查找所有子事项
- 支持多层级的父子关系
- 确保获取所有必要的字段信息

### 2. 完整数据复制
- 复制事项基本信息
- 复制自定义字段
- 复制关注人信息
- 复制多人指派信息
- 复制附件（如果启用）
- 复制检查清单（如果启用）

### 3. 父子关系维护
- 正确映射原事项ID到新事项ID
- 维护复制后的父子关系结构
- 确保层级关系的完整性

## 新增方法

### `getIssuesCustomFields(array $issueIds): array`
批量获取事项的自定义字段数据
- 参数：事项ID数组
- 返回：按事项ID分组的自定义字段数组

### `getIssuesWatchers(array $issueIds): array`
批量获取事项的关注人数据
- 参数：事项ID数组
- 返回：按事项ID分组的关注人数组

### `getIssuesAssignedUsers(array $issueIds): array`
批量获取事项的多人指派数据
- 参数：事项ID数组
- 返回：按事项ID分组的指派用户数组

## 修改的方法

### `getAllChildIssues(array $issueIds): array`
- 增加了字段选择，确保获取复制所需的所有字段
- 改进了注释和参数说明

### `batchCopyIssue(array $params): array`
- 当`copy_children`为true时，自动获取所有子事项
- 将子事项添加到复制列表中
- 正确处理所有事项的父子关系
- 增加了统计信息返回

## 使用方式

```php
$params = [
    'target_project_id' => 123,
    'issues' => [
        ['id' => 1, 'subject' => '主事项1', ...],
        ['id' => 2, 'subject' => '主事项2', ...],
    ],
    'copy_children' => true,  // 启用子事项复制
    'copy_attachments' => true,
    'copy_checklists' => true,
    'copy_description' => false,
    'link_to_original' => true,
];

$result = $issueService->batchCopyIssue($params);
```

## 返回结果

```php
[
    'success' => true,
    'message' => '批量复制完成',
    'copied_count' => 15,           // 总复制数量
    'original_issue_count' => 5,    // 原始选中事项数量
    'child_issue_count' => 10,      // 子事项数量
    'copied_issues' => [...],       // 复制的事项列表
    'failed_issues' => [...],       // 失败的事项（如果有）
    'failed_count' => 0,
]
```

## 注意事项

1. **事务安全**：所有操作在数据库事务中进行，确保数据一致性
2. **性能优化**：使用批量查询减少数据库访问次数
3. **错误处理**：详细的错误记录和异常处理
4. **内存管理**：对于大量子事项的情况，注意内存使用

## 测试

创建了基本的单元测试文件：`test/Cases/IssueServiceTest.php`

运行测试：
```bash
php bin/hyperf.php test test/Cases/IssueServiceTest.php
```
