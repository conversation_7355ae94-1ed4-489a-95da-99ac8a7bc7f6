# 子事项状态和日期同步功能

## 概述

在复制事项时，对于通过 `copy_children` 功能搜寻到的子事项，实现了以下功能：
1. 状态默认初始化为 `ISSUE_STATUS_CREATE_NEW = 1`（新建状态）
2. 开始日期和完成日期同步为对应父事项的值

## 修改内容

### 1. 常量定义

在 `app/Constants/IssueCode.php` 中已存在常量：
```php
/* 事项状态 - 新建 */
const ISSUE_STATUS_CREATE_NEW = 1;
```

### 2. 子事项状态和日期处理

修改了 `batchCopyIssue` 方法中的子事项处理逻辑：

```php
// 获取父事项的日期信息用于同步
$parentDates = $this->getParentDatesForChildIssues($issues, $filteredChildIssues);

// 在格式化子事项时
$childIssuesFormatted[] = [
    'id' => $issueId,
    'subject' => $childIssue['subject'],
    'tracker_id' => $childIssue['tracker_id'],
    'status_id' => \App\Constants\IssueCode::ISSUE_STATUS_CREATE_NEW, // 设置为新建状态
    'priority_id' => $childIssue['priority_id'],
    'category_id' => $childIssue['category_id'] ?? 0,
    'fixed_version_id' => $childIssue['fixed_version_id'],
    'assigned_to_id' => $childIssue['assigned_to_id'],
    'start_date' => $parentDate['start_date'] ?? null, // 同步父事项的开始日期
    'due_date' => $parentDate['due_date'] ?? null,     // 同步父事项的完成日期
    'class_id' => $childIssue['class_id'] ?? 0,
    'parent_id' => $childIssue['parent_id'],
    // 其他字段...
];
```

### 3. 新增辅助方法

添加了 `getParentDatesForChildIssues` 方法来获取父事项的日期信息：

```php
/**
 * 获取父事项的日期信息用于子事项同步
 * @param array $parentIssues 父事项数组
 * @param array $childIssues 子事项数组
 * @return array 按父事项ID分组的日期信息
 */
private function getParentDatesForChildIssues(array $parentIssues, array $childIssues): array
```

## 功能特点

### 1. 状态初始化
- **目标状态**: `ISSUE_STATUS_CREATE_NEW = 1`
- **适用范围**: 仅对通过 `getAllChildIssues` 搜寻到的子事项
- **保持原则**: 用户明确勾选的事项保持原有状态

### 2. 日期同步
- **开始日期**: 同步对应父事项的 `start_date`
- **完成日期**: 同步对应父事项的 `due_date`
- **匹配逻辑**: 根据子事项的 `parent_id` 匹配对应的父事项日期

### 3. 智能匹配
- **优先级**: 优先使用用户传入的父事项日期信息
- **补充查询**: 如果子事项的父事项不在用户勾选列表中，从数据库查询
- **兜底机制**: 如果找不到对应父事项，使用第一个可用的父事项日期

## 处理逻辑

### 1. 日期获取流程
```
1. 从用户传入的父事项中提取日期信息
2. 识别子事项中可能存在的其他父事项ID
3. 从数据库查询缺失的父事项日期信息
4. 构建完整的父事项日期映射表
```

### 2. 子事项处理流程
```
1. 遍历过滤后的子事项
2. 根据parent_id查找对应的父事项日期
3. 设置状态为ISSUE_STATUS_CREATE_NEW
4. 同步父事项的开始日期和完成日期
5. 保持其他字段不变
```

## 修改文件

- `app/Core/Services/Project/IssueService.php`
  - 修改子事项格式化逻辑（第1632-1669行）
  - 新增 `getParentDatesForChildIssues` 方法（第3527-3562行）

## 示例效果

### 修改前
```php
// 子事项保持原有状态和日期
'status_id' => $childIssue['status_id'],        // 可能是各种状态
'start_date' => $childIssue['start_date'],      // 原有开始日期
'due_date' => $childIssue['due_date'],          // 原有完成日期
```

### 修改后
```php
// 子事项统一初始化状态并同步父事项日期
'status_id' => \App\Constants\IssueCode::ISSUE_STATUS_CREATE_NEW, // 统一为新建状态(1)
'start_date' => $parentDate['start_date'] ?? null,                // 父事项的开始日期
'due_date' => $parentDate['due_date'] ?? null,                    // 父事项的完成日期
```

## 业务价值

1. **状态统一**: 所有复制的子事项都从新建状态开始，便于管理
2. **日期同步**: 子事项的时间安排与父事项保持一致
3. **工作流程**: 符合项目管理的层级关系和时间规划
4. **用户体验**: 减少手动调整子事项状态和日期的工作量

## 注意事项

1. **仅影响搜寻到的子事项**: 用户明确勾选的事项保持原有设置
2. **日期可能为空**: 如果父事项没有设置日期，子事项的日期也会为空
3. **状态工作流**: 确保新建状态(1)在项目的工作流中是有效的起始状态
4. **权限考虑**: 复制后的子事项状态变更需要符合用户的权限设置
