# 检查清单自定义字段复制问题修复

## 问题描述

在复制事项的检查清单时，出现了以下错误：

```
SQLSTATE[22032]: <<Unknown error>>: 3140 Invalid JSON text: "Invalid value." at position 0 in value for column 'checklist_items.custom_fields'.
```

错误原因是在批量插入检查清单项时，`custom_fields` 字段的值是数组类型，但在批量插入时被转换为字符串 "Array"，而不是有效的 JSON 字符串。

## 错误日志分析

从错误日志可以看到：
- SQL 插入语句中 `custom_fields` 的值显示为 `Array`
- MySQL 的 JSON 字段无法接受 "Array" 这样的字符串
- 需要将数组正确转换为 JSON 字符串

## 问题根源

在 `CheckListService.php` 的 `copyChecklistsToIssue` 方法中：

```php
// 问题代码
'custom_fields' => $sourceItem->custom_fields,
```

当 `$sourceItem->custom_fields` 是数组时，在批量插入 `insert()` 方法中会被 PHP 自动转换为字符串 "Array"，而不是有效的 JSON。

## 解决方案

修改了 `copyChecklistsToIssue` 方法，在准备批量插入数据时正确处理 `custom_fields` 字段：

```php
// 处理 custom_fields 字段，确保它是有效的 JSON 格式
$customFields = $sourceItem->custom_fields;
if (is_array($customFields)) {
    $customFields = json_encode($customFields);
} elseif (is_string($customFields) && $customFields !== '') {
    // 验证是否为有效的 JSON
    $decoded = json_decode($customFields, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $customFields = null; // 如果不是有效的 JSON，设为 null
    }
} else {
    $customFields = null;
}
```

## 修改文件

- `app/Core/Services/Project/CheckList/CheckListService.php`

## 修改内容

### 修改位置
第828-856行的 `copyChecklistsToIssue` 方法

### 修改前
```php
'custom_fields' => $sourceItem->custom_fields,
```

### 修改后
```php
// 处理 custom_fields 字段，确保它是有效的 JSON 格式
$customFields = $sourceItem->custom_fields;
if (is_array($customFields)) {
    $customFields = json_encode($customFields);
} elseif (is_string($customFields) && $customFields !== '') {
    // 验证是否为有效的 JSON
    $decoded = json_decode($customFields, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $customFields = null; // 如果不是有效的 JSON，设为 null
    }
} else {
    $customFields = null;
}

// 在数组中使用处理后的值
'custom_fields' => $customFields,
```

## 处理逻辑

1. **数组类型**: 使用 `json_encode()` 转换为 JSON 字符串
2. **字符串类型**: 验证是否为有效的 JSON，无效则设为 null
3. **其他类型**: 设为 null
4. **空值处理**: 空字符串或 null 都设为 null

## 为什么其他地方没有问题

在 `saveChecklistItems` 方法中使用的是模型的 `create()` 和 `save()` 方法，这些方法会自动处理类型转换（通过模型的 `$casts` 属性）。

但是 `insert()` 方法是直接的数据库操作，绕过了模型的类型转换机制，因此需要手动处理。

## 测试建议

1. 测试复制包含自定义字段的检查清单
2. 测试不同类型的 custom_fields 值（数组、JSON字符串、null、空字符串）
3. 验证复制后的检查清单项数据完整性

## 相关模型

- `ChecklistItemModel`: `custom_fields` 字段被定义为 `'custom_fields' => 'json'` 类型
- 数据库表: `checklist_items.custom_fields` 字段类型为 JSON
