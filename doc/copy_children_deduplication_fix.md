# 子事项复制去重修复

## 问题描述

在实现 `copy_children` 功能时，发现当用户勾选的事项中包含了某些子事项，而这些子事项又通过 `getAllChildIssues` 方法被获取到时，会导致数据重复复制。

## 问题场景

假设有以下事项结构：
```
事项A (ID: 1)
├── 子事项B (ID: 2)
├── 子事项C (ID: 3)
    └── 孙事项D (ID: 4)
```

如果用户勾选了事项A和子事项B，并启用了 `copy_children`：
1. 用户勾选：[事项A, 子事项B]
2. `getAllChildIssues([1])` 会获取到：[子事项B, 子事项C, 孙事项D]
3. 最终复制列表：[事项A, 子事项B, 子事项B, 子事项C, 孙事项D] ❌

这导致子事项B被重复复制。

## 解决方案

### 1. 去重逻辑

在获取子事项后，过滤掉已经在用户勾选列表中的事项：

```php
// 过滤掉已经在用户勾选列表中的子事项，避免重复
$filteredChildIssues = array_filter($childIssues, function($childIssue) use ($selectedIssueIds) {
    return !in_array($childIssue['id'], $selectedIssueIds);
});
```

### 2. 优先级原则

**以用户勾选的数据为准**：
- 如果用户明确勾选了某个事项，使用用户提供的数据
- 只有未被用户勾选的子事项才从数据库获取

### 3. 日志记录

添加了去重信息的日志记录，便于调试：

```php
// 记录去重信息
$originalChildCount = count($childIssues);
$filteredChildCount = count($filteredChildIssues);
if ($originalChildCount > $filteredChildCount) {
    Log::get('system', 'info')->info('子事项去重处理', [
        'original_child_count' => $originalChildCount,
        'filtered_child_count' => $filteredChildCount,
        'removed_duplicates' => $originalChildCount - $filteredChildCount,
        'selected_issue_ids' => $selectedIssueIds
    ]);
}
```

## 修改文件

- `app/Core/Services/Project/IssueService.php`

## 修改内容

### 修改位置
第1614-1655行的 `batchCopyIssue` 方法

### 核心改动

1. **添加去重过滤**：
   ```php
   // 修改前
   $childIssues = $this->getAllChildIssues($selectedIssueIds);
   // 直接使用所有子事项
   
   // 修改后  
   $childIssues = $this->getAllChildIssues($selectedIssueIds);
   $filteredChildIssues = array_filter($childIssues, function($childIssue) use ($selectedIssueIds) {
       return !in_array($childIssue['id'], $selectedIssueIds);
   });
   ```

2. **更新统计逻辑**：
   ```php
   // 修改前
   $childIssueCount = count($allIssuesToCopy) - $originalIssueCount;
   
   // 修改后
   $totalIssuesToCopy = count($allIssuesToCopy);
   $childIssueCount = $totalIssuesToCopy - $originalIssueCount;
   ```

## 处理流程

1. **获取用户勾选的事项ID列表**
2. **获取所有子事项**（通过递归查询）
3. **过滤重复事项**（移除已在勾选列表中的子事项）
4. **格式化子事项数据**（获取关联数据并转换格式）
5. **合并到复制列表**（用户勾选 + 过滤后的子事项）
6. **执行复制操作**

## 示例

### 修复前
```
用户勾选：[事项A(1), 子事项B(2)]
子事项查询结果：[子事项B(2), 子事项C(3), 孙事项D(4)]
最终复制列表：[事项A(1), 子事项B(2), 子事项B(2), 子事项C(3), 孙事项D(4)]
结果：子事项B重复复制 ❌
```

### 修复后
```
用户勾选：[事项A(1), 子事项B(2)]
子事项查询结果：[子事项B(2), 子事项C(3), 孙事项D(4)]
过滤后子事项：[子事项C(3), 孙事项D(4)]
最终复制列表：[事项A(1), 子事项B(2), 子事项C(3), 孙事项D(4)]
结果：无重复，以用户勾选数据为准 ✅
```

## 优势

1. **避免重复**：确保每个事项只被复制一次
2. **用户优先**：以用户明确勾选的数据为准
3. **完整性**：仍然能够复制所有相关的子事项
4. **可追踪**：通过日志记录去重过程
5. **性能优化**：减少不必要的重复操作

## 测试建议

1. 测试用户勾选父事项和部分子事项的情况
2. 测试复杂的多层级事项结构
3. 验证去重后的父子关系是否正确
4. 检查统计信息的准确性
